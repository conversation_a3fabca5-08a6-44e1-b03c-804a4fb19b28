[2025-06-20 16:06:40.943 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.948 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.983 +07:00 INF] Cache warmup completed in 35.106ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.983 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.983 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.985 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.986 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.162 +07:00 INF] Generating security dashboard data from "2025-05-21T09:06:41.0000000Z" to "2025-06-20T09:06:41.0000000Z" {"SourceContext":"DecorStore.API.Services.SecurityDashboardService","ActionId":"ef21652f-ebc5-490b-b642-78a636aedceb","ActionName":"DecorStore.API.Controllers.SecurityDashboardController.GetDashboardData (DecorStore.API)","RequestId":"0HNDFR6TFG7IB","RequestPath":"/api/SecurityDashboard/dashboard","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.167 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.167 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
