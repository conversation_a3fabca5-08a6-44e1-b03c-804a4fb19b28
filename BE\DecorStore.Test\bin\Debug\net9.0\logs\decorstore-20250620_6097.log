[2025-06-20 16:25:12.764 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.768 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.801 +07:00 INF] Cache warmup completed in 32.5704ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.801 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.801 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.802 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.803 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.988 +07:00 INF] Generating security dashboard data from "2025-06-13T09:25:12.9885100Z" to "2025-06-20T09:25:12.9885111Z" {"SourceContext":"DecorStore.API.Services.SecurityDashboardService","ActionId":"799e59d6-9f67-4484-b78d-f9bbabd8143e","ActionName":"DecorStore.API.Controllers.SecurityDashboardController.GetDashboardData (DecorStore.API)","RequestId":"0HNDFRH6SDMV4","RequestPath":"/api/SecurityDashboard/dashboard","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.990 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.990 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
