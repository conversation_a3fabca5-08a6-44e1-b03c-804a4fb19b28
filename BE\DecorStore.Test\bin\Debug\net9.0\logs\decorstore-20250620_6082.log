[2025-06-20 16:25:11.782 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:11.786 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:11.811 +07:00 INF] Cache warmup completed in 25.0966ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:11.812 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:11.812 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:11.813 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:11.813 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:11.827 +07:00 WRN] Request failed with error: User with this email already exists, ErrorCode: USER_ALREADY_EXISTS, CorrelationId: 00-8eee3874a5aefd9bae4fd76532589531-863d40acbac7d1d9-00 {"SourceContext":"DecorStore.API.Controllers.AuthController","ActionId":"d97099b0-5fe0-4600-a1fd-f0782a84742c","ActionName":"DecorStore.API.Controllers.AuthController.Register (DecorStore.API)","RequestId":"0HNDFRH6SDMTI","RequestPath":"/api/Auth/register","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:11.983 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:11.983 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
