[2025-06-20 16:06:40.517 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.522 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.556 +07:00 INF] Cache warmup completed in 33.882ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.556 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.556 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.557 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.559 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.747 +07:00 WRN] Request failed with error: INVALID_STATUS, ErrorCode: Order status cannot be null or empty, CorrelationId: 00-8547910b24a6f65a46f2f9d26f9d5d61-dc453e0e968d6bcf-00 {"SourceContext":"DecorStore.API.Controllers.OrderController","ActionId":"557347a5-3d4a-4035-bdd9-f6204a5e6f17","ActionName":"DecorStore.API.Controllers.OrderController.UpdateOrderStatus (DecorStore.API)","RequestId":"0HNDFR6TFG7I4","RequestPath":"/api/Order/1/status","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.758 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.758 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
