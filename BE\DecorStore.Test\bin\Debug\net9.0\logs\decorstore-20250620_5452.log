[2025-06-20 16:06:41.801 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.804 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.820 +07:00 INF] Cache warmup completed in 15.4821ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.820 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.820 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.821 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.823 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.980 +07:00 WRN] Redis connection not available for pattern removal: products* {"SourceContext":"DecorStore.API.Extensions.DistributedCacheService","ActionId":"8693dd81-7d32-4a25-8f0d-512830f4c9a0","ActionName":"DecorStore.API.Controllers.PerformanceController.ClearCacheByPrefix (DecorStore.API)","RequestId":"0HNDFR6TFG7IV","RequestPath":"/api/Performance/cache/clear/products","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.980 +07:00 INF] Cache cleared for prefix products by admin user {"SourceContext":"DecorStore.API.Controllers.PerformanceController","ActionId":"8693dd81-7d32-4a25-8f0d-512830f4c9a0","ActionName":"DecorStore.API.Controllers.PerformanceController.ClearCacheByPrefix (DecorStore.API)","RequestId":"0HNDFR6TFG7IV","RequestPath":"/api/Performance/cache/clear/products","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.981 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.981 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
