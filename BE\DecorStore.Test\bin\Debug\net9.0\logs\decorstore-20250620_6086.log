[2025-06-20 16:25:12.062 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.065 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.094 +07:00 INF] Cache warmup completed in 28.9607ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.094 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.094 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.095 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.095 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.274 +07:00 WRN] Request failed with error: INVALID_STATUS, ErrorCode: Order status cannot be null or empty, CorrelationId: 00-8923546737c0d66c014aad97ea3a83c4-110f27b2ece2b23b-00 {"SourceContext":"DecorStore.API.Controllers.OrderController","ActionId":"83c107db-7334-42b6-b9f0-abc401f86a56","ActionName":"DecorStore.API.Controllers.OrderController.UpdateOrderStatus (DecorStore.API)","RequestId":"0HNDFRH6SDMUE","RequestPath":"/api/Order/1/status","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.276 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:12.276 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
