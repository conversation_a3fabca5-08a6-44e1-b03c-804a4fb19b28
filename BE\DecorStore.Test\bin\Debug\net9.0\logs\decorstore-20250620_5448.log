[2025-06-20 16:06:41.475 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.479 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.503 +07:00 INF] Cache warmup completed in 24.1937ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.503 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.503 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.504 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.505 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.671 +07:00 INF] Generating security dashboard data from "2025-06-13T09:06:41.6714443Z" to "2025-06-20T09:06:41.6714447Z" {"SourceContext":"DecorStore.API.Services.SecurityDashboardService","ActionId":"f2fbd597-b667-44b0-8886-ab449c677e09","ActionName":"DecorStore.API.Controllers.SecurityDashboardController.GetDashboardData (DecorStore.API)","RequestId":"0HNDFR6TFG7IN","RequestPath":"/api/SecurityDashboard/dashboard","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.673 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:41.673 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
