[2025-06-20 16:06:40.522 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.526 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.558 +07:00 INF] Cache warmup completed in 31.246ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.558 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.558 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.560 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.560 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.733 +07:00 INF] Generating security dashboard data from "2025-06-13T09:06:40.7336974Z" to "2025-06-20T09:06:40.7336983Z" {"SourceContext":"DecorStore.API.Services.SecurityDashboardService","ActionId":"5d784075-eaaa-45ea-8467-76e0d2f6c3d0","ActionName":"DecorStore.API.Controllers.SecurityDashboardController.GetDashboardData (DecorStore.API)","RequestId":"0HNDFR6TFG7I3","RequestPath":"/api/SecurityDashboard/dashboard","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.735 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:06:40.736 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
