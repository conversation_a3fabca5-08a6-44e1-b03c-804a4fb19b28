[2025-06-20 16:25:13.130 +07:00 INF] Cache warmup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:13.133 +07:00 INF] Starting cache warmup process {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:13.144 +07:00 INF] Cache warmup completed in 10.8982ms {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheWarmupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:13.144 +07:00 INF] Cache cleanup service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.CacheCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:13.144 +07:00 INF] Performance monitoring service started {"SourceContext":"DecorStore.API.Services.BackgroundServices.PerformanceMonitoringService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:13.145 +07:00 INF] Token Cleanup Service started {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:13.146 +07:00 INF] Token cleanup completed successfully {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:13.321 +07:00 WRN] Redis connection not available for pattern removal: products* {"SourceContext":"DecorStore.API.Extensions.DistributedCacheService","ActionId":"ee6b7a13-751b-4a55-9801-b6a6b61d69cb","ActionName":"DecorStore.API.Controllers.PerformanceController.ClearCacheByPrefix (DecorStore.API)","RequestId":"0HNDFRH6SDMVE","RequestPath":"/api/Performance/cache/clear/products","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:13.321 +07:00 INF] Cache cleared for prefix products by admin user {"SourceContext":"DecorStore.API.Controllers.PerformanceController","ActionId":"ee6b7a13-751b-4a55-9801-b6a6b61d69cb","ActionName":"DecorStore.API.Controllers.PerformanceController.ClearCacheByPrefix (DecorStore.API)","RequestId":"0HNDFRH6SDMVE","RequestPath":"/api/Performance/cache/clear/products","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:13.323 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
[2025-06-20 16:25:13.323 +07:00 INF] Token Cleanup Service is stopping {"SourceContext":"DecorStore.API.Services.TokenCleanupService","Application":"DecorStore.API","Environment":"Test"}
